package com.sub.subm.api.service.assistant.model;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.J<PERSON><PERSON>eader;
import com.alibaba.fastjson2.JSONWriter;
import com.alibaba.fastjson2.filter.Filter;
import com.sub.common.constant.Constants;
import com.sub.subm.api.service.assistant.context.SessionContext;
import com.sub.subm.dal.dao.chat.ChatMemoryDao;
import com.sub.subm.dal.entity.generate.chat.ChatMemoryPo;

import dev.langchain4j.data.message.ChatMessage;
import dev.langchain4j.store.memory.chat.ChatMemoryStore;
import lombok.extern.slf4j.Slf4j;

/**
 * 持久化聊天记忆存储实现
 * 支持多用户/多会话的聊天记忆管理
 * 数据存储在数据库中
 */
@Slf4j
@Component
public class PersistentChatMemoryStore implements ChatMemoryStore {

    @Autowired
    private ChatMemoryDao chatMemoryDao;

    @Autowired
    private SessionContext sessionContext;

    @Override
    public List<ChatMessage> getMessages(Object memoryId) {
        log.debug("获取聊天记忆, memoryId: {}", memoryId);

        try {
            String sessionId = memoryId.toString();
            ChatMemoryPo chatMemory = chatMemoryDao.selectBySessionId(sessionId);

            if (chatMemory == null || !chatMemory.isValid()) {
                log.debug("memoryId {} 没有历史消息，返回空列表", memoryId);
                return new ArrayList<>();
            }

            // 反序列化消息列表
            List<ChatMessage> messages = deserializeMessages(chatMemory.getMessages());
            log.debug("memoryId {} 找到 {} 条历史消息", memoryId, messages.size());
            return messages;

        } catch (Exception e) {
            log.error("获取聊天记忆失败, memoryId: {}", memoryId, e);
            return new ArrayList<>();
        }
    }

    @Override
    public void updateMessages(Object memoryId, List<ChatMessage> messages) {
        log.debug("更新聊天记忆, memoryId: {}, 消息数量: {}", memoryId, messages != null ? messages.size() : 0);

        try {
            String sessionId = memoryId.toString();

            if (messages == null || messages.isEmpty()) {
                // 删除聊天记忆
                chatMemoryDao.softDeleteBySessionId(sessionId);
                log.debug("删除聊天记忆, sessionId: {}", sessionId);
                return;
            }

            // 序列化消息列表
            String messagesJson = serializeMessages(messages);

            // 查询是否已存在记录
            ChatMemoryPo existingMemory = chatMemoryDao.selectBySessionId(sessionId);

            if (existingMemory != null && existingMemory.isValid()) {
                // 更新现有记录
                existingMemory.setMessages(messagesJson);
                existingMemory.updateMessageCount(messages.size());
                chatMemoryDao.update(existingMemory);
                log.debug("更新聊天记忆, sessionId: {}, 消息数量: {}", sessionId, messages.size());
            } else {
                // 创建新记录
                String userId = getUserIdFromSession(sessionId);
                ChatMemoryPo newMemory = new ChatMemoryPo(sessionId, userId);
                newMemory.setMessages(messagesJson);
                newMemory.updateMessageCount(messages.size());
                chatMemoryDao.insert(newMemory);
                log.debug("创建聊天记忆, sessionId: {}, userId: {}, 消息数量: {}", sessionId, userId, messages.size());
            }

        } catch (Exception e) {
            log.error("更新聊天记忆失败, memoryId: {}", memoryId, e);
        }
    }

    @Override
    public void deleteMessages(Object memoryId) {
        log.debug("删除聊天记忆, memoryId: {}", memoryId);

        try {
            String sessionId = memoryId.toString();
            ChatMemoryPo existingMemory = chatMemoryDao.selectBySessionId(sessionId);

            if (existingMemory != null && existingMemory.isValid()) {
                chatMemoryDao.softDeleteBySessionId(sessionId);
                log.info("成功删除memoryId {} 的聊天记忆", memoryId);
            } else {
                log.debug("memoryId {} 的聊天记忆不存在或已删除", memoryId);
            }
        } catch (Exception e) {
            log.error("删除聊天记忆失败, memoryId: {}", memoryId, e);
        }
    }

    /**
     * 获取当前存储的会话数量
     */
    public int getSessionCount() {
        try {
            return chatMemoryDao.countTotal();
        } catch (Exception e) {
            log.error("获取会话数量失败", e);
            return 0;
        }
    }

    /**
     * 清空所有聊天记忆
     */
    public void clearAll() {
        try {
            int count = chatMemoryDao.countTotal();
            // 这里可以实现批量软删除，但需要添加相应的DAO方法
            log.warn("clearAll方法暂未实现批量删除，当前会话数量: {}", count);
        } catch (Exception e) {
            log.error("清空聊天记忆失败", e);
        }
    }

    /**
     * 序列化消息列表为JSON字符串
     */
    private String serializeMessages(List<ChatMessage> messages) {
        try {
            // 使用FastJSON序列化，包含类型信息
            return JSON.toJSONString(messages, JSONWriter.Feature.WriteClassName);
        } catch (Exception e) {
            log.error("序列化消息列表失败", e);
            throw new RuntimeException("序列化消息列表失败", e);
        }
    }

    /**
     * 反序列化JSON字符串为消息列表
     */
    @SuppressWarnings("unchecked")
    private List<ChatMessage> deserializeMessages(String messagesJson) {
        try {
            if (messagesJson == null || messagesJson.trim().isEmpty()) {
                return new ArrayList<>();
            }

            // 使用FastJSON反序列化，支持类型信息
            Filter autoTypeFilter = JSONReader.autoTypeFilter(Constants.JSON_WHITELIST_STR);
            JSONReader.Context context = JSONReader.Context.of(autoTypeFilter);
            JSONReader reader = JSONReader.of(messagesJson, context);
            List<ChatMessage> messages = (List<ChatMessage>) reader.readArray(ChatMessage.class);
            return messages != null ? messages : new ArrayList<>();
        } catch (Exception e) {
            log.error("反序列化消息列表失败, json: {}", messagesJson, e);
            return new ArrayList<>();
        }
    }

    /**
     * 从会话ID获取用户ID
     */
    private String getUserIdFromSession(String sessionId) {
        try {
            Session session = sessionContext.getSession(sessionId);
            return session.getUserId();
        } catch (Exception e) {
            log.warn("无法从sessionId获取userId, sessionId: {}", sessionId, e);
            return "unknown";
        }
    }
}
