package com.sub.subm.api;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;

@SpringBootApplication
@ComponentScan({"com.sub.subm.api", "com.sub.subm.dal", "com.sub.common", "com.sub.framework"})
@MapperScan({"com.sub.subm.dal.dao.**"})
public class SubMApiApplication {
    public static void main(String[] args) {
        SpringApplication.run(SubMApiApplication.class, args);
    }
}
