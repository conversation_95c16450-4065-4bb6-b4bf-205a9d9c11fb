package com.sub.subm.api.service.assistant.context;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.springframework.stereotype.Component;

import com.sub.subm.dal.dao.user.UserDao;
import com.sub.subm.dal.entity.generate.user.UserPo;

import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 用户上下文管理器
 * 用于通过memoryId管理用户信息，替代ThreadLocal机制
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class UserContextManager {


    private final UserDao userDao;
    /**
     * memoryId -> 用户信息的映射
     */
    private final Map<Object, UserInfo> userContextMap = new ConcurrentHashMap<>();

    /**
     * 用户信息模型
     */
    @Data
    public static class UserInfo {
        private final String userId;
        private final String userName;
        private final String sessionId;
        private final String currency;

        public UserInfo(String userId, String userName, String sessionId, String currency) {
            this.userId = userId;
            this.userName = userName;
            this.sessionId = sessionId;
            this.currency = currency;
        }
    }

    /**
     * 设置用户上下文
     */
    public void setUserContext(Object memoryId, String userId, String userName, String sessionId, String currency) {
        UserInfo userInfo = new UserInfo(userId, userName, sessionId, currency);
        userContextMap.put(memoryId, userInfo);
        log.debug("设置用户上下文, memoryId: {}, userId: {}, userName: {}",
                memoryId, userId, userName);
    }

    /**
     * 设置用户上下文（从Session对象）
     */
    public void setUserContext(Object memoryId, String userId) {
        UserPo userPo = userDao.getByIdUser(userId);
        setUserContext(memoryId, userId, userPo.getName(), memoryId.toString(), userPo.getCurrency());
    }

    /**
     * 获取用户信息
     */
    public UserInfo getUserInfo(Object memoryId) {
        return userContextMap.get(memoryId);
    }

    /**
     * 获取用户ID
     */
    public String getUserId(Object memoryId) {
        UserInfo userInfo = getUserInfo(memoryId);
        return userInfo != null ? userInfo.getUserId() : null;
    }

    /**
     * 获取用户名
     */
    public String getUserName(Object memoryId) {
        UserInfo userInfo = getUserInfo(memoryId);
        return userInfo != null ? userInfo.getUserName() : null;
    }

    /**
     * 获取会话ID
     */
    public String getSessionId(Object memoryId) {
        UserInfo userInfo = getUserInfo(memoryId);
        return userInfo != null ? userInfo.getSessionId() : null;
    }

    /**
     * 检查用户是否存在
     */
    public boolean hasUser(Object memoryId) {
        return getUserInfo(memoryId) != null;
    }

    /**
     * 验证用户权限
     */
    public void validateUser(Object memoryId) throws IllegalStateException {
        UserInfo userInfo = getUserInfo(memoryId);
        if (userInfo == null) {
            throw new IllegalStateException("用户上下文不存在，memoryId: " + memoryId);
        }

        if (userInfo.getUserId() == null || userInfo.getUserId().trim().isEmpty()) {
            throw new IllegalStateException("用户未登录，请先登录");
        }
    }

    /**
     * 移除用户上下文
     */
    public void removeUserContext(Object memoryId) {
        UserInfo removed = userContextMap.remove(memoryId);
        if (removed != null) {
            log.debug("移除用户上下文, memoryId: {}, userId: {}",
                    memoryId, removed.getUserId());
        }
    }

    /**
     * 清除所有用户上下文
     */
    public void clearAllUserContexts() {
        int count = userContextMap.size();
        userContextMap.clear();
        log.info("清除所有用户上下文, 共清理 {} 个", count);
    }

    /**
     * 获取当前活跃用户数量
     */
    public int getActiveUserCount() {
        return userContextMap.size();
    }

    /**
     * 获取用户信息摘要
     */
    public String getUserSummary(Object memoryId) {
        UserInfo userInfo = getUserInfo(memoryId);
        if (userInfo == null) {
            return "无用户信息";
        }

        return String.format("MemoryId: %s, 用户ID: %s, 用户名: %s, 会话ID: %s",
                memoryId,
                userInfo.getUserId() != null ? userInfo.getUserId() : "未登录",
                userInfo.getUserName() != null ? userInfo.getUserName() : "未知",
                userInfo.getSessionId());
    }
}
