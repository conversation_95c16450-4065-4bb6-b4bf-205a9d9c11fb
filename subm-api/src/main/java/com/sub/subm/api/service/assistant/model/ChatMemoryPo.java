package com.sub.subm.api.service.assistant.model;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sub.common.core.common.BaseEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 聊天记忆实体类
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "聊天记忆数据")
public class ChatMemoryPo extends BaseEntity implements Serializable {

    @Schema(description = "会话ID (同时也是LangChain4j的memoryId)")
    private String sessionId;

    @Schema(description = "用户ID")
    private String userId;

    @Schema(description = "消息列表的JSON字符串")
    private String messages;

    @Schema(description = "消息总数")
    private Integer messageCount;

    @Schema(description = "最后活跃时间")
    private Long lastActiveTime;

    /**
     * 构造函数 - 用于创建新的聊天记忆记录
     */
    public ChatMemoryPo(String sessionId, String userId) {
        this.sessionId = sessionId;
        this.userId = userId;
        this.messageCount = 0;
        this.lastActiveTime = System.currentTimeMillis();
        this.setCreateTime(System.currentTimeMillis());
        this.setUpdateTime(System.currentTimeMillis());
        this.setEnable(1);
    }

    /**
     * 更新最后活跃时间
     */
    public void updateLastActiveTime() {
        this.lastActiveTime = System.currentTimeMillis();
        this.setUpdateTime(System.currentTimeMillis());
    }

    /**
     * 更新消息数量
     */
    public void updateMessageCount(int count) {
        this.messageCount = count;
        this.updateLastActiveTime();
    }

    /**
     * 检查记录是否有效
     */
    @JsonIgnore
    public boolean isValid() {
        return this.getEnable() != null && this.getEnable() == 1;
    }

    /**
     * 软删除记录
     */
    public void softDelete() {
        this.setEnable(0);
        this.setUpdateTime(System.currentTimeMillis());
    }
}
