package com.sub.subm.api.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sub.subm.api.serializer.I18nBeanSerializerModifier;

/**
 * Jackson国际化配置
 * 配置Jackson使用国际化序列化器
 */
@Configuration
public class JacksonI18nConfiguration {

    private static final Logger log = LoggerFactory.getLogger(JacksonI18nConfiguration.class);

    @Autowired
    private I18nBeanSerializerModifier i18nBeanSerializerModifier;

    /**
     * 配置支持国际化的ObjectMapper
     * 
     * @param builder Jackson2ObjectMapperBuilder
     * @return 配置好的ObjectMapper
     */
    @Bean
    @Primary
    public ObjectMapper objectMapper(Jackson2ObjectMapperBuilder builder) {
        log.info("配置Jackson国际化序列化器...");
        
        ObjectMapper objectMapper = builder.build();
        
        // 注册国际化序列化器修改器
        objectMapper.setSerializerFactory(
            objectMapper.getSerializerFactory()
                .withSerializerModifier(i18nBeanSerializerModifier)
        );
        
        log.info("Jackson国际化序列化器配置完成");
        return objectMapper;
    }

    /**
     * 获取国际化序列化器修改器的统计信息
     * 
     * @return 统计信息
     */
    public String getI18nStatistics() {
        return String.format("国际化缓存大小: %d", i18nBeanSerializerModifier.getCacheSize());
    }

    /**
     * 清除国际化缓存
     */
    public void clearI18nCache() {
        i18nBeanSerializerModifier.clearCache();
        log.info("已清除Jackson国际化缓存");
    }
}
