package com.sub.subm.api.service.assistant.service;

import java.util.ArrayList;
import java.util.List;

import org.springframework.stereotype.Service;

import com.sub.common.exception.BaseException;
import com.sub.subm.api.service.assistant.assistant.ChatAssistant;
import com.sub.subm.api.service.assistant.context.UserContextManager;
import com.sub.subm.api.service.assistant.model.PersistentChatMemoryStore;
import com.sub.subm.api.service.assistant.tools.ProductTool;
import com.sub.subm.api.service.assistant.tools.RemindTool;
import com.sub.subm.api.service.assistant.tools.UserTool;
import com.sub.subm.dal.dao.chat.ChatMemoryDao;

import dev.langchain4j.data.message.ChatMessage;
import dev.langchain4j.memory.chat.ChatMemoryProvider;
import dev.langchain4j.memory.chat.MessageWindowChatMemory;
import dev.langchain4j.model.openai.OpenAiStreamingChatModel;
import dev.langchain4j.service.AiServices;
import dev.langchain4j.service.TokenStream;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 聊天服务类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ChatService {

    private final OpenAiStreamingChatModel openAiStreamingChatModel;
    private final UserTool userTool;
    private final RemindTool remindTool;
    private final ProductTool productTool;
    private final UserContextManager userContextManager;
    private final SessionService sessionService;
    private final ChatMemoryDao chatMemoryDao;

    /**
     * 全局AI助手实例
     * 通过chatMemoryProvider支持多用户/多会话
     */
    private ChatAssistant globalChatAssistant;

    /**
     * 全局持久化聊天记忆存储
     */
    private PersistentChatMemoryStore globalChatMemoryStore;

    /**
     * 初始化全局AI助手实例
     */
    @PostConstruct
    public void initChatAssistant() {
        log.info("初始化全局AI助手实例");

        // 创建全局的持久化存储实例
        globalChatMemoryStore = new PersistentChatMemoryStore(chatMemoryDao);

        ChatMemoryProvider chatMemoryProvider = memoryId -> {
            log.debug("为memoryId创建ChatMemory: {}", memoryId);
            try {
                return MessageWindowChatMemory.builder()
                        .id(memoryId)
                        .maxMessages(20)
                        .chatMemoryStore(globalChatMemoryStore)
                        .build();
            } catch (Exception e) {
                log.error("创建ChatMemory失败, memoryId: {}", memoryId, e);
                return MessageWindowChatMemory.withMaxMessages(20);
            }
        };

        globalChatAssistant = AiServices.builder(ChatAssistant.class)
                .streamingChatModel(openAiStreamingChatModel)
                .chatMemoryProvider(chatMemoryProvider)
                .tools(userTool, remindTool, productTool)
                .build();

        log.info("全局AI助手实例初始化完成，支持持久化聊天记忆");
    }

    /**
     * 流式聊天
     */
    public TokenStream chatStream(String sessionId, String message) throws BaseException {
        log.info("开始流式聊天, sessionId: {}, message: {}", sessionId, message);

        try {
            // 设置用户上下文到UserContextManager
            Object memoryId = sessionId; // 使用sessionId作为memoryId
            userContextManager.setUserContext(memoryId, sessionService.getUserIdBySessionId(sessionId));

            // 使用全局助手实例，通过memoryId区分不同会话
            return globalChatAssistant.chat(memoryId, message);
        } catch (Exception e) {
            log.error("流式聊天失败, sessionId: {}, message: {}", sessionId, message, e);
            throw new BaseException("聊天失败: " + e.getMessage());
        }
    }

    /**
     * 获取历史对话
     */
    public List<ChatMessage> getHistory(String sessionId) {
        if (globalChatMemoryStore == null) {
            log.warn("全局聊天记忆存储未初始化");
            return new ArrayList<>();
        }

        Object memoryId = sessionId; // 使用sessionId作为memoryId
        return globalChatMemoryStore.getMessages(memoryId);
    }

    /**
     * 清除指定会话的聊天记忆
     */
    public void clearHistory(String sessionId) {
        if (globalChatMemoryStore != null) {
            Object memoryId = sessionId;
            globalChatMemoryStore.deleteMessages(memoryId);
            log.info("清除会话聊天记忆, sessionId: {}", sessionId);
        }
    }

    /**
     * 获取当前活跃的聊天会话数量
     */
    public int getActiveChatSessionCount() {
        return globalChatMemoryStore != null ? globalChatMemoryStore.getSessionCount() : 0;
    }
}
