package com.sub.subm.api.service.assistant.service;

import org.springframework.stereotype.Service;

import com.sub.subm.dal.dao.chat.ChatMemoryDao;

import cn.hutool.core.util.IdUtil;
import lombok.RequiredArgsConstructor;



@Service
@RequiredArgsConstructor
public class SessionService {

    private final ChatMemoryDao chatMemoryDao;
    /**
     * 创建session
     * 
     * @return sessionId
     */
    public String createSession(String userId, String userName) {

        String seesionId = IdUtil.fastSimpleUUID();
        
        return null;
    }
}
