package com.sub.subm.api.service.assistant.service;

import org.springframework.stereotype.Service;

import com.sub.subm.dal.dao.chat.ChatMemoryDao;
import com.sub.subm.dal.entity.generate.chat.ChatMemoryPo;

import cn.hutool.core.util.IdUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;



@Service
@Slf4j
@RequiredArgsConstructor
public class SessionService {

    private final ChatMemoryDao chatMemoryDao;
    /**
     * 创建session
     * 
     * @return sessionId
     */
    public String createSession(String userId, String userName) {

        String seesionId = IdUtil.fastSimpleUUID();
        ChatMemoryPo chatMemoryPo = new ChatMemoryPo();
        chatMemoryPo.setSessionId(seesionId);
        chatMemoryPo.setUserId(userId);
        try {
            chatMemoryDao.insert(chatMemoryPo);
            return seesionId;
        } catch (Exception e) {
            log.error("创建session失败, userId: {}, userName: {}", userId, userName, e);
            return null;
        }
    }
    /**
     * 根据sessionId获取userId
     * @param sessionId
     * @return
     */
    public String getUserIdBySessionId(String sessionId) {
        ChatMemoryPo chatMemoryPo = chatMemoryDao.selectBySessionId(sessionId);
        return chatMemoryPo.getUserId();
    }
}
