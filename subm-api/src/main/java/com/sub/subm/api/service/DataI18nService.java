package com.sub.subm.api.service;

import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.ConcurrentHashMap;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.sub.common.utils.StringUtils;
import com.sub.subm.dal.entity.generate.product.CurrencyPo;
import com.sub.subm.dal.entity.generate.product.ProductCategoryPo;
import com.sub.subm.dal.entity.generate.product.RegionPo;

import jakarta.annotation.PostConstruct;
import jakarta.servlet.http.HttpServletRequest;

/**
 * 数据国际化服务
 * 用于将数据库中的固定数据转换为多语言版本
 * 直接读取资源文件，避免Spring i18n机制的复杂性
 */
@Service
public class DataI18nService {

    private static final Logger log = LoggerFactory.getLogger(DataI18nService.class);

    // 缓存各语言的Properties文件
    private final Map<String, Properties> propertiesCache = new ConcurrentHashMap<>();

    /**
     * 初始化加载所有语言的资源文件
     */
    @PostConstruct
    public void init() {
        loadProperties("messages.properties", "zh_CN");  // 默认中文
        loadProperties("messages_en.properties", "en_US");  // 英文
        loadProperties("messages_ja.properties", "ja_JP");  // 日语
        loadProperties("messages_ko.properties", "ko_KR");  // 韩语
        loadProperties("messages_fr.properties", "fr_FR");  // 法语
        log.info("加载国际化资源完成...");
    }

    /**
     * 加载指定的Properties文件
     * @param fileName 文件名
     * @param locale 语言标识
     */
    private void loadProperties(String fileName, String locale) {
        try (InputStream inputStream = getClass().getClassLoader().getResourceAsStream("i18n/" + fileName)) {
            if (inputStream != null) {
                Properties properties = new Properties();
                // 使用UTF-8编码读取
                properties.load(new InputStreamReader(inputStream, StandardCharsets.UTF_8));
                propertiesCache.put(locale, properties);
                log.info("成功加载国际化资源文件: {} -> {}", fileName, locale);
            } else {
                log.warn("未找到国际化资源文件: {}", fileName);
            }
        } catch (IOException e) {
            log.error("加载国际化资源文件失败: {}", fileName, e);
        }
    }

    /**
     * 获取国际化消息
     * @param key 国际化key
     * @return 翻译后的文本，如果找不到则返回null
     */
    private String getMessage(String key) {
        String locale = getCurrentLocale();
        Properties properties = propertiesCache.get(locale);

        if (properties != null) {
            return properties.getProperty(key);
        }

        // 如果当前语言没有找到，尝试使用默认语言（中文）
        if (!"zh_CN".equals(locale)) {
            Properties defaultProperties = propertiesCache.get("zh_CN");
            if (defaultProperties != null) {
                return defaultProperties.getProperty(key);
            }
        }

        return null;
    }

    /**
     * 获取当前语言环境
     * @return 语言标识
     */
    private String getCurrentLocale() {
        try {
            // 直接从HTTP请求中获取Accept-Language，避免触发LocaleContextHolder
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                String acceptLanguage = request.getHeader("Accept-Language");

                if (StringUtils.isNotEmpty(acceptLanguage)) {
                    String language = acceptLanguage.trim().toLowerCase();

                    // 映射到我们支持的语言
                    switch (language) {
                        case "zh":
                            return "zh_CN";
                        case "en":
                            return "en_US";
                        case "ja":
                            return "ja_JP";
                        case "ko":
                            return "ko_KR";
                        case "fr":
                            return "fr_FR";
                        default:
                            return "zh_CN"; // 默认中文
                    }
                }
            }

            return "zh_CN"; // 默认中文
        } catch (Exception e) {
            log.warn("获取当前语言环境失败，使用默认中文: {}", e.getMessage());
            return "zh_CN";
        }
    }

    /**
     * 国际化货币数据
     * @param currencyPo 货币对象
     * @return 国际化后的货币对象
     */
    public CurrencyPo internationalizeCurrency(CurrencyPo currencyPo) {
        if (currencyPo == null) {
            return null;
        }
        
        CurrencyPo result = new CurrencyPo();
        // 复制所有原始属性
        result.setId(currencyPo.getId());
        result.setSymbol(currencyPo.getSymbol());
        result.setExchangeRate(currencyPo.getExchangeRate());
        result.setSimple(currencyPo.getSimple());
        result.setCreateTime(currencyPo.getCreateTime());
        result.setCreator(currencyPo.getCreator());
        result.setUpdateTime(currencyPo.getUpdateTime());
        result.setUpdator(currencyPo.getUpdator());
        result.setEnable(currencyPo.getEnable());
        
        // 国际化名称
        String i18nKey = "currency." + currencyPo.getSimple() + ".name";
        String localizedName = getMessage(i18nKey);
        // 如果找不到国际化内容，使用原始名称
        result.setName(StringUtils.isNotEmpty(localizedName) ? localizedName : currencyPo.getName());
        
        return result;
    }

    /**
     * 国际化地区数据
     * @param regionPo 地区对象
     * @return 国际化后的地区对象
     */
    public RegionPo internationalizeRegion(RegionPo regionPo) {
        if (regionPo == null) {
            return null;
        }
        
        RegionPo result = new RegionPo();
        // 复制所有原始属性
        result.setId(regionPo.getId());
        result.setCurrencyId(regionPo.getCurrencyId());
        result.setAreaCode(regionPo.getAreaCode());
        result.setCountryCode(regionPo.getCountryCode());
        result.setIconUrl(regionPo.getIconUrl());
        result.setCreator(regionPo.getCreator());
        result.setCreateTime(regionPo.getCreateTime());
        result.setUpdator(regionPo.getUpdator());
        result.setUpdateTime(regionPo.getUpdateTime());
        result.setPopular(regionPo.getPopular());
        result.setEnable(regionPo.getEnable());
        result.setCurrenyCode(regionPo.getCurrenyCode());
        
        // 国际化名称 - 优先使用国家代码，如果没有则使用ID
        String i18nKey;
        if (StringUtils.isNotEmpty(regionPo.getCountryCode())) {
            i18nKey = "region." + regionPo.getCountryCode() + ".name";
        } else {
            i18nKey = "region." + regionPo.getId() + ".name";
        }
        
        String localizedName = getMessage(i18nKey);
        // 如果找不到国际化内容，使用原始名称
        if (StringUtils.isNotEmpty(localizedName)) {
            result.setName(localizedName);
        } else {
            // 直接使用英文名称作为默认值
            result.setName(StringUtils.isNotEmpty(regionPo.getName()) ? regionPo.getName() : regionPo.getNameCn());
        }
        
        // 保留原始字段用于兼容性
        result.setNameCn(regionPo.getNameCn());
        
        return result;
    }

    /**
     * 国际化产品分类数据
     * @param categoryPo 产品分类对象
     * @return 国际化后的产品分类对象
     */
    public ProductCategoryPo internationalizeProductCategory(ProductCategoryPo categoryPo) {
        if (categoryPo == null) {
            return null;
        }
        
        ProductCategoryPo result = new ProductCategoryPo();
        // 复制所有原始属性
        result.setId(categoryPo.getId());
        result.setProducer(categoryPo.getProducer());
        result.setIconUrl(categoryPo.getIconUrl());
        result.setCreateTime(categoryPo.getCreateTime());
        result.setCreator(categoryPo.getCreator());
        result.setUpdateTime(categoryPo.getUpdateTime());
        result.setUpdator(categoryPo.getUpdator());
        result.setParams(categoryPo.getParams());
        result.setIsAdmin(categoryPo.getIsAdmin());
        result.setLanguage(categoryPo.getLanguage());
        result.setCategoryList(categoryPo.getCategoryList());
        result.setSuffixTable(categoryPo.getSuffixTable());
        result.setSearch(categoryPo.getSearch());
        
        // 国际化名称 - 使用ID作为key
        String i18nKey = "category." + categoryPo.getId() + ".name";
        String localizedName = getMessage(i18nKey);

        // 如果找不到国际化内容，使用原始名称
        if (StringUtils.isNotEmpty(localizedName)) {
            result.setName(localizedName);
        } else {
            // 直接使用英文名称作为默认值
            result.setName(StringUtils.isNotEmpty(categoryPo.getName()) ? categoryPo.getName() : categoryPo.getNameCn());
        }
        
        // 保留原始字段用于兼容性
        result.setNameCn(categoryPo.getNameCn());
        
        return result;
    }
}
