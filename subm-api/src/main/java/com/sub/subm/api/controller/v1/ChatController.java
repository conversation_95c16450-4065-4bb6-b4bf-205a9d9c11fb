package com.sub.subm.api.controller.v1;

import java.util.Map;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.sub.common.core.common.CommonResult;
import com.sub.common.exception.BaseException;
import com.sub.subm.api.security.SecurityUtils;
import com.sub.subm.api.service.assistant.context.UserContextManager;
import com.sub.subm.api.service.assistant.service.ChatService;
import com.sub.subm.api.service.assistant.service.SessionService;

import cn.hutool.core.util.StrUtil;
import dev.langchain4j.service.TokenStream;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;

/**
 * 聊天控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/chat")
@RequiredArgsConstructor
public class ChatController {

    private final ChatService chatService;
    private final SessionService sessionService;
    private final UserContextManager userContextManager;

    /**
     * 创建新的聊天会话
     */
    @PostMapping("/session")
    public CommonResult<Map<String, String>> createSession(HttpServletRequest request) throws BaseException {

        try {
            String userId = SecurityUtils.getUserId();
            String userName = SecurityUtils.getLoginUser().getUser().getName();

            if (StrUtil.isAllBlank(userId, userName)) {
                throw new BaseException("未获取到用户信息上下文，请先登录");
            }

            String sessionId = sessionService.createSession(userId, userName);
            
            return CommonResult.ok(Map.of("sessionId", sessionId));
        } catch (Exception e) {
            log.error("创建会话失败", e);
            return CommonResult.error(e.getMessage());
        }
    }

    /**
     * 流式聊天
     */
    @GetMapping(value = "/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<String> chatStream(
            @RequestParam("sessionId") String sessionId,
            @RequestParam("message") String message) {

        try {

            TokenStream tokenStream = chatService.chatStream(sessionId, message);

            return Flux.<String>create(sink -> {
                tokenStream
                        .onPartialResponse(partialResponse -> {
                            sink.next("[PARTIAL]" + partialResponse + "\n\n");
                        })
                        .onToolExecuted(toolExecution -> {
                            sink.next("[TOOL_EXECUTED]" + toolExecution.result() + "\n\n");
                        })
                        .onCompleteResponse(completeResponse -> {
                            sink.next("[DONE]" + completeResponse + "\n\n");
                            sink.complete();
                        })
                        .onError(error -> {
                            log.error("流式聊天出错", error);
                            sink.error(error);
                        })
                        .start();
            }).doFinally(signalType -> {
                log.debug("流式聊天结束，清理会话上下文，信号类型: {}", signalType);
                userContextManager.removeUserContext(sessionId);
            });
        } catch (Exception e) {
            log.error("流式聊天失败, sessionId: {}, message: {}", sessionId, message, e);
            return Flux.error(e);
        }
    }
}
