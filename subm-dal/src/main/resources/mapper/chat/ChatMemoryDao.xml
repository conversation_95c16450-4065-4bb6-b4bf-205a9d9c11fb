<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sub.subm.dal.dao.chat.ChatMemoryDao">

    <!-- 结果映射 -->
    <resultMap id="ChatMemoryResultMap" type="com.sub.subm.dal.entity.generate.chat.ChatMemoryPo">
        <id column="session_id" property="sessionId" jdbcType="VARCHAR"/>
        <result column="user_id" property="userId" jdbcType="VARCHAR"/>
        <result column="messages" property="messages" jdbcType="LONGVARCHAR"/>
        <result column="message_count" property="messageCount" jdbcType="INTEGER"/>
        <result column="last_active_time" property="lastActiveTime" jdbcType="BIGINT"/>
        <result column="create_time" property="createTime" jdbcType="BIGINT"/>
        <result column="update_time" property="updateTime" jdbcType="BIGINT"/>
        <result column="enable" property="enable" jdbcType="TINYINT"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        session_id, user_id, messages, message_count, last_active_time,
        create_time, update_time, enable
    </sql>

    <!-- 根据会话ID查询 -->
    <select id="selectBySessionId" resultMap="ChatMemoryResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM chat_memory
        WHERE session_id = #{sessionId}
        AND enable = 1
    </select>

    <!-- 插入记录 -->
    <insert id="insert" parameterType="com.sub.subm.dal.entity.generate.chat.ChatMemoryPo">
        INSERT INTO chat_memory (
            session_id, user_id, messages, message_count, last_active_time,
            create_time, update_time, enable
        ) VALUES (
            #{sessionId}, #{userId}, #{messages}, #{messageCount}, #{lastActiveTime},
            #{createTime}, #{updateTime}, #{enable}
        )
    </insert>

    <!-- 更新记录 -->
    <update id="update" parameterType="com.sub.subm.dal.entity.generate.chat.ChatMemoryPo">
        UPDATE chat_memory
        SET messages = #{messages},
            message_count = #{messageCount},
            last_active_time = #{lastActiveTime},
            update_time = #{updateTime}
        WHERE session_id = #{sessionId}
        AND enable = 1
    </update>

    <!-- 物理删除 -->
    <delete id="deleteBySessionId">
        DELETE FROM chat_memory
        WHERE session_id = #{sessionId}
    </delete>

    <!-- 软删除 -->
    <update id="softDeleteBySessionId">
        UPDATE chat_memory
        SET enable = 0,
            update_time = UNIX_TIMESTAMP() * 1000
        WHERE session_id = #{sessionId}
        AND enable = 1
    </update>

    <!-- 根据用户ID查询 -->
    <select id="selectByUserId" resultMap="ChatMemoryResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM chat_memory
        WHERE user_id = #{userId}
        AND enable = 1
        ORDER BY last_active_time DESC
    </select>

    <!-- 根据用户ID软删除 -->
    <update id="softDeleteByUserId">
        UPDATE chat_memory
        SET enable = 0,
            update_time = UNIX_TIMESTAMP() * 1000
        WHERE user_id = #{userId}
        AND enable = 1
    </update>

    <!-- 清理旧数据 -->
    <delete id="cleanupOldMemories">
        DELETE FROM chat_memory
        WHERE last_active_time &lt; #{cutoffTime}
        AND enable = 1
    </delete>

    <!-- 统计用户记录数 -->
    <select id="countByUserId" resultType="int">
        SELECT COUNT(*)
        FROM chat_memory
        WHERE user_id = #{userId}
        AND enable = 1
    </select>

    <!-- 统计总记录数 -->
    <select id="countTotal" resultType="int">
        SELECT COUNT(*)
        FROM chat_memory
        WHERE enable = 1
    </select>

    <!-- 分页查询 -->
    <select id="selectPage" resultMap="ChatMemoryResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM chat_memory
        WHERE enable = 1
        ORDER BY last_active_time DESC
        LIMIT #{offset}, #{limit}
    </select>

    <!-- 根据活跃时间范围查询 -->
    <select id="selectByActiveTimeRange" resultMap="ChatMemoryResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM chat_memory
        WHERE last_active_time BETWEEN #{startTime} AND #{endTime}
        AND enable = 1
        ORDER BY last_active_time DESC
    </select>

</mapper>
