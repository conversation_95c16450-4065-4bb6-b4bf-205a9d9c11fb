package com.sub.subm.dal.entity.generate.chat;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sub.common.core.common.BaseEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 聊天会话实体类
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "聊天会话数据")
public class ChatSessionPo extends BaseEntity implements Serializable {

    @Schema(description = "用户ID")
    private String userId;

    @Schema(description = "用户名称")
    private String userName;

    @Schema(description = "用户默认货币")
    private String currency;

    @Schema(description = "会话状态 (ACTIVE, INACTIVE, EXPIRED)")
    private String status;

    @Schema(description = "会话配置信息(JSON格式)")
    private String sessionConfig;

    @Schema(description = "消息总数")
    private Integer messageCount;

    @Schema(description = "最后消息时间戳")
    private Long lastMessageTime;

    @Schema(description = "过期时间戳")
    private Long expireTime;

    /**
     * 检查会话是否过期
     */
    @JsonIgnore
    public boolean isExpired() {
        if (expireTime == null) {
            return false;
        }
        return System.currentTimeMillis() > expireTime;
    }

    /**
     * 检查会话是否活跃
     */
    @JsonIgnore
    public boolean isActive() {
        return "ACTIVE".equals(status) && !isExpired();
    }

    /**
     * 更新最后消息时间
     */
    public void updateLastMessageTime() {
        this.lastMessageTime = System.currentTimeMillis();
    }

    /**
     * 增加消息计数
     */
    public void incrementMessageCount() {
        if (this.messageCount == null) {
            this.messageCount = 0;
        }
        this.messageCount++;
    }

    /**
     * 设置过期时间（基于超时分钟数）
     */
    public void setExpireTimeFromTimeout(Integer timeoutMinutes) {
        if (timeoutMinutes != null && timeoutMinutes > 0) {
            this.expireTime = System.currentTimeMillis() + (timeoutMinutes * 60 * 1000L);
        }
    }
}
