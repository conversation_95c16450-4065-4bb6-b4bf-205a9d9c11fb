package com.sub.subm.dal.dao.chat;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.sub.subm.dal.entity.generate.chat.ChatMemoryPo;


/**
 * 聊天记忆数据访问接口
 */
@Mapper
public interface ChatMemoryDao {

    /**
     * 根据会话ID查询聊天记忆
     */
    ChatMemoryPo selectBySessionId(@Param("sessionId") String sessionId);

    /**
     * 插入聊天记忆记录
     */
    int insert(ChatMemoryPo chatMemory);

    /**
     * 更新聊天记忆记录
     */
    int update(ChatMemoryPo chatMemory);

    /**
     * 根据会话ID删除聊天记忆 (物理删除)
     */
    int deleteBySessionId(@Param("sessionId") String sessionId);

    /**
     * 根据会话ID软删除聊天记忆
     */
    int softDeleteBySessionId(@Param("sessionId") String sessionId);

    /**
     * 根据用户ID查询所有聊天记忆
     */
    List<ChatMemoryPo> selectByUserId(@Param("userId") String userId);

    /**
     * 根据用户ID软删除所有聊天记忆
     */
    int softDeleteByUserId(@Param("userId") String userId);

    /**
     * 清理超过指定天数的聊天记忆
     */
    int cleanupOldMemories(@Param("cutoffTime") Long cutoffTime);

    /**
     * 统计用户的聊天记忆数量
     */
    int countByUserId(@Param("userId") String userId);

    /**
     * 统计总的聊天记忆数量
     */
    int countTotal();

    /**
     * 分页查询聊天记忆 (用于管理后台)
     */
    List<ChatMemoryPo> selectPage(@Param("offset") int offset, @Param("limit") int limit);

    /**
     * 根据最后活跃时间范围查询
     */
    List<ChatMemoryPo> selectByActiveTimeRange(
        @Param("startTime") Long startTime, 
        @Param("endTime") Long endTime
    );
}
