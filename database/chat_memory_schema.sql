-- =============================================
-- 聊天记忆持久化数据库设计
-- =============================================

-- 1. 聊天会话表
CREATE TABLE `chat_session` (
  `id` varchar(64) NOT NULL COMMENT '会话ID (使用sessionId)',
  `user_id` varchar(64) NOT NULL COMMENT '用户ID',
  `user_name` varchar(100) DEFAULT NULL COMMENT '用户名称',
  `currency` varchar(10) DEFAULT NULL COMMENT '用户默认货币',
  `status` varchar(20) DEFAULT 'ACTIVE' COMMENT '会话状态 (ACTIVE, INACTIVE, EXPIRED)',
  `session_config` json DEFAULT NULL COMMENT '会话配置信息',
  `message_count` int DEFAULT 0 COMMENT '消息总数',
  `last_message_time` bigint DEFAULT NULL COMMENT '最后消息时间戳',
  `create_time` bigint NOT NULL COMMENT '创建时间戳',
  `update_time` bigint DEFAULT NULL COMMENT '更新时间戳',
  `expire_time` bigint DEFAULT NULL COMMENT '过期时间戳',
  `enable` tinyint DEFAULT 1 COMMENT '是否启用 (0:删除 1:正常)',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_expire_time` (`expire_time`),
  KEY `idx_last_message_time` (`last_message_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='聊天会话表';

-- 2. 聊天消息表
CREATE TABLE `chat_message` (
  `id` varchar(64) NOT NULL COMMENT '消息ID',
  `session_id` varchar(64) NOT NULL COMMENT '会话ID',
  `user_id` varchar(64) NOT NULL COMMENT '用户ID',
  `message_type` varchar(20) NOT NULL COMMENT '消息类型 (USER, AI, SYSTEM, TOOL_EXECUTION_REQUEST, TOOL_EXECUTION_RESULT)',
  `message_role` varchar(20) DEFAULT NULL COMMENT '消息角色 (user, assistant, system, tool)',
  `content` longtext NOT NULL COMMENT '消息内容',
  `content_type` varchar(20) DEFAULT 'TEXT' COMMENT '内容类型 (TEXT, JSON, TOOL_CALL)',
  `tool_name` varchar(100) DEFAULT NULL COMMENT '工具名称 (如果是工具调用)',
  `tool_arguments` json DEFAULT NULL COMMENT '工具参数 (如果是工具调用)',
  `tool_result` longtext DEFAULT NULL COMMENT '工具执行结果',
  `sequence_number` int NOT NULL COMMENT '消息序号 (在会话中的顺序)',
  `token_count` int DEFAULT 0 COMMENT 'Token数量',
  `metadata` json DEFAULT NULL COMMENT '消息元数据',
  `create_time` bigint NOT NULL COMMENT '创建时间戳',
  `enable` tinyint DEFAULT 1 COMMENT '是否启用 (0:删除 1:正常)',
  PRIMARY KEY (`id`),
  KEY `idx_session_id` (`session_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_message_type` (`message_type`),
  KEY `idx_sequence` (`session_id`, `sequence_number`),
  KEY `idx_create_time` (`create_time`),
  CONSTRAINT `fk_chat_message_session` FOREIGN KEY (`session_id`) REFERENCES `chat_session` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='聊天消息表';

-- 3. 聊天记忆统计表 (可选，用于性能优化)
CREATE TABLE `chat_memory_stats` (
  `id` varchar(64) NOT NULL COMMENT '统计ID',
  `session_id` varchar(64) NOT NULL COMMENT '会话ID',
  `user_id` varchar(64) NOT NULL COMMENT '用户ID',
  `total_messages` int DEFAULT 0 COMMENT '总消息数',
  `user_messages` int DEFAULT 0 COMMENT '用户消息数',
  `ai_messages` int DEFAULT 0 COMMENT 'AI消息数',
  `tool_calls` int DEFAULT 0 COMMENT '工具调用次数',
  `total_tokens` int DEFAULT 0 COMMENT '总Token数',
  `first_message_time` bigint DEFAULT NULL COMMENT '首条消息时间',
  `last_message_time` bigint DEFAULT NULL COMMENT '最后消息时间',
  `session_duration` bigint DEFAULT 0 COMMENT '会话持续时间(秒)',
  `create_time` bigint NOT NULL COMMENT '创建时间戳',
  `update_time` bigint DEFAULT NULL COMMENT '更新时间戳',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_session_id` (`session_id`),
  KEY `idx_user_id` (`user_id`),
  CONSTRAINT `fk_chat_stats_session` FOREIGN KEY (`session_id`) REFERENCES `chat_session` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='聊天记忆统计表';

-- 4. 创建索引优化查询性能
-- 复合索引：按会话ID和序号查询消息
CREATE INDEX `idx_session_sequence` ON `chat_message` (`session_id`, `sequence_number`);

-- 复合索引：按用户ID和创建时间查询
CREATE INDEX `idx_user_create_time` ON `chat_message` (`user_id`, `create_time`);

-- 复合索引：按会话ID和消息类型查询
CREATE INDEX `idx_session_type` ON `chat_message` (`session_id`, `message_type`);

-- 5. 数据清理相关索引
-- 用于定时清理过期数据
CREATE INDEX `idx_session_expire` ON `chat_session` (`expire_time`, `enable`);
CREATE INDEX `idx_message_cleanup` ON `chat_message` (`create_time`, `enable`);

-- =============================================
-- 示例数据插入
-- =============================================

-- 插入示例会话
INSERT INTO `chat_session` (
  `id`, `user_id`, `user_name`, `currency`, `status`, 
  `session_config`, `message_count`, `last_message_time`, 
  `create_time`, `update_time`, `expire_time`
) VALUES (
  'session_123456', 'user_001', '张三', 'CNY', 'ACTIVE',
  '{"sessionTimeoutMinutes": 30, "maxMessageHistory": 20, "enableStreaming": true}',
  0, NULL, UNIX_TIMESTAMP() * 1000, NULL, 
  (UNIX_TIMESTAMP() + 1800) * 1000  -- 30分钟后过期
);

-- 插入示例消息
INSERT INTO `chat_message` (
  `id`, `session_id`, `user_id`, `message_type`, `message_role`,
  `content`, `content_type`, `sequence_number`, `token_count`,
  `create_time`
) VALUES (
  'msg_001', 'session_123456', 'user_001', 'USER', 'user',
  '你好，我想查看我的订阅列表', 'TEXT', 1, 15,
  UNIX_TIMESTAMP() * 1000
);

-- =============================================
-- 数据清理存储过程
-- =============================================

DELIMITER $$

-- 清理过期会话和消息的存储过程
CREATE PROCEDURE `CleanupExpiredChatData`()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE expired_session_id VARCHAR(64);
    DECLARE expired_cursor CURSOR FOR 
        SELECT id FROM chat_session 
        WHERE expire_time < UNIX_TIMESTAMP() * 1000 
        AND enable = 1;
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;

    -- 开始事务
    START TRANSACTION;
    
    -- 删除过期的消息 (级联删除会自动处理)
    DELETE FROM chat_session 
    WHERE expire_time < UNIX_TIMESTAMP() * 1000 
    AND enable = 1;
    
    -- 记录清理日志
    SELECT ROW_COUNT() as cleaned_sessions;
    
    COMMIT;
END$$

-- 清理超过指定天数的历史数据
CREATE PROCEDURE `CleanupOldChatData`(IN days_to_keep INT)
BEGIN
    DECLARE cutoff_time BIGINT;
    SET cutoff_time = (UNIX_TIMESTAMP() - (days_to_keep * 24 * 3600)) * 1000;
    
    START TRANSACTION;
    
    -- 删除旧消息
    DELETE FROM chat_message 
    WHERE create_time < cutoff_time;
    
    -- 删除没有消息的会话
    DELETE FROM chat_session 
    WHERE id NOT IN (SELECT DISTINCT session_id FROM chat_message);
    
    COMMIT;
END$$

DELIMITER ;

-- =============================================
-- 定时任务建议 (需要在应用层实现)
-- =============================================

/*
建议创建以下定时任务：

1. 每10分钟清理过期会话：
   CALL CleanupExpiredChatData();

2. 每天凌晨清理30天前的历史数据：
   CALL CleanupOldChatData(30);

3. 每周更新统计信息：
   UPDATE chat_memory_stats SET 
     total_messages = (SELECT COUNT(*) FROM chat_message WHERE session_id = chat_memory_stats.session_id),
     last_message_time = (SELECT MAX(create_time) FROM chat_message WHERE session_id = chat_memory_stats.session_id);
*/
