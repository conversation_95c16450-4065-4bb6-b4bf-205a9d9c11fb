-- =============================================
-- 聊天记忆持久化数据库表
-- =============================================

CREATE TABLE `chat_memory` (
  `session_id` varchar(64) NOT NULL COMMENT '会话ID (同时也是LangChain4j的memoryId)',
  `user_id` varchar(64) NOT NULL COMMENT '用户ID',
  `messages` longtext NOT NULL COMMENT 'List<ChatMessage>的JSON序列化',
  `message_count` int DEFAULT 0 COMMENT '消息总数',
  `last_active_time` bigint NOT NULL COMMENT '最后活跃时间',
  `create_time` bigint NOT NULL COMMENT '创建时间',
  `update_time` bigint NOT NULL COMMENT '更新时间',
  `enable` tinyint DEFAULT 1 COMMENT '是否启用 (0:删除 1:正常)',
  PRIMARY KEY (`session_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_last_active_time` (`last_active_time`),
  KEY `idx_enable` (`enable`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='聊天记忆表';

-- 创建索引用于数据清理
CREATE INDEX `idx_cleanup` ON `chat_memory` (`last_active_time`, `enable`);

-- =============================================
-- 数据清理存储过程
-- =============================================

DELIMITER $$

-- 清理超过指定天数的聊天记忆
CREATE PROCEDURE `CleanupOldChatMemory`(IN days_to_keep INT)
BEGIN
    DECLARE cutoff_time BIGINT;
    DECLARE deleted_count INT DEFAULT 0;
    
    SET cutoff_time = (UNIX_TIMESTAMP() - (days_to_keep * 24 * 3600)) * 1000;
    
    START TRANSACTION;
    
    -- 删除旧的聊天记忆
    DELETE FROM chat_memory 
    WHERE last_active_time < cutoff_time 
    AND enable = 1;
    
    SET deleted_count = ROW_COUNT();
    
    -- 记录清理日志
    SELECT CONCAT('清理了 ', deleted_count, ' 条超过 ', days_to_keep, ' 天的聊天记忆') as result;
    
    COMMIT;
END$$

-- 清理指定用户的聊天记忆
CREATE PROCEDURE `CleanupUserChatMemory`(IN target_user_id VARCHAR(64))
BEGIN
    DECLARE deleted_count INT DEFAULT 0;
    
    START TRANSACTION;
    
    -- 软删除用户的聊天记忆
    UPDATE chat_memory 
    SET enable = 0, update_time = UNIX_TIMESTAMP() * 1000
    WHERE user_id = target_user_id 
    AND enable = 1;
    
    SET deleted_count = ROW_COUNT();
    
    -- 记录清理日志
    SELECT CONCAT('清理了用户 ', target_user_id, ' 的 ', deleted_count, ' 条聊天记忆') as result;
    
    COMMIT;
END$$

DELIMITER ;

-- =============================================
-- 示例数据
-- =============================================

-- 插入示例聊天记忆
INSERT INTO `chat_memory` (
  `session_id`, `user_id`, `messages`, `message_count`, 
  `last_active_time`, `create_time`, `update_time`
) VALUES (
  'session_example_001', 'user_001', 
  '[{"type":"dev.langchain4j.data.message.UserMessage","text":"你好，我想查看我的订阅列表"},{"type":"dev.langchain4j.data.message.AiMessage","text":"好的，我来帮您查看订阅列表..."}]',
  2, 
  UNIX_TIMESTAMP() * 1000, 
  UNIX_TIMESTAMP() * 1000, 
  UNIX_TIMESTAMP() * 1000
);
