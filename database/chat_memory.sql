-- =============================================
-- 聊天记忆持久化数据库表
-- =============================================

CREATE TABLE `chat_memory` (
  `session_id` varchar(64) NOT NULL COMMENT '会话ID (同时也是LangChain4j的memoryId)',
  `user_id` varchar(64) NOT NULL COMMENT '用户ID',
  `messages` longtext NOT NULL COMMENT 'List<ChatMessage>的JSON序列化',
  `message_count` int DEFAULT 0 COMMENT '消息总数',
  `last_active_time` bigint NOT NULL COMMENT '最后活跃时间',
  `create_time` bigint NOT NULL COMMENT '创建时间',
  `update_time` bigint NOT NULL COMMENT '更新时间',
  `enable` tinyint DEFAULT 1 COMMENT '是否启用 (0:删除 1:正常)',
  PRIMARY KEY (`session_id`),
  KEY `idx_user_id` (`user_id`),
  <PERSON>EY `idx_last_active_time` (`last_active_time`),
  KEY `idx_enable` (`enable`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='聊天记忆表';
